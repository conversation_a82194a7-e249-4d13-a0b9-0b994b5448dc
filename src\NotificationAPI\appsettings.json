{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "DatabaseProvider": "SQLite", "ConnectionStrings": {"DefaultConnection": "Data Source=notifications.db", "PostgreSQLConnection": "Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432", "SqlServerConnection": "Server=(localdb)\\mssqllocaldb;Database=NotificationsDB;Trusted_Connection=true;MultipleActiveResultSets=true"}}