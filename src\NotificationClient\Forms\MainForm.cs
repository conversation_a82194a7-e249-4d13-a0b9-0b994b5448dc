using NotificationClient.Services;
using NotificationShared.DTOs;
using NotificationShared.Models;

namespace NotificationClient.Forms
{
    public partial class MainForm : Form
    {
        private readonly NotificationApiClient _apiClient;
        private readonly SignalRService _signalRService;
        private string _currentUserId = "user1"; // Para demonstração
        
        private ListView _notificationsList;
        private Label _unreadCountLabel;
        private Button _markAllReadButton;
        private Button _refreshButton;
        private Button _createNotificationButton;
        private TextBox _userIdTextBox;
        private Button _connectButton;

        public MainForm()
        {
            _apiClient = new NotificationApiClient();
            _signalRService = new SignalRService();
            
            InitializeComponent();
            SetupSignalREvents();
        }

        private void InitializeComponent()
        {
            this.Text = "Sistema de Notificações Kodiak";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Panel superior para controles
            var topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.LightGray
            };

            // User ID
            var userLabel = new Label
            {
                Text = "User ID:",
                Location = new Point(10, 15),
                Size = new Size(60, 23)
            };

            _userIdTextBox = new TextBox
            {
                Text = _currentUserId,
                Location = new Point(75, 12),
                Size = new Size(100, 23)
            };

            _connectButton = new Button
            {
                Text = "Conectar",
                Location = new Point(185, 10),
                Size = new Size(80, 27)
            };
            _connectButton.Click += ConnectButton_Click;

            // Contagem de não lidas
            _unreadCountLabel = new Label
            {
                Text = "Não lidas: 0",
                Location = new Point(280, 15),
                Size = new Size(100, 23),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            // Botões
            _refreshButton = new Button
            {
                Text = "Atualizar",
                Location = new Point(390, 10),
                Size = new Size(80, 27)
            };
            _refreshButton.Click += RefreshButton_Click;

            _markAllReadButton = new Button
            {
                Text = "Marcar Todas como Lidas",
                Location = new Point(480, 10),
                Size = new Size(150, 27)
            };
            _markAllReadButton.Click += MarkAllReadButton_Click;

            _createNotificationButton = new Button
            {
                Text = "Criar Notificação",
                Location = new Point(640, 10),
                Size = new Size(120, 27)
            };
            _createNotificationButton.Click += CreateNotificationButton_Click;

            topPanel.Controls.AddRange(new Control[] { 
                userLabel, _userIdTextBox, _connectButton, _unreadCountLabel, 
                _refreshButton, _markAllReadButton, _createNotificationButton 
            });

            // Lista de notificações
            _notificationsList = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false
            };

            _notificationsList.Columns.Add("ID", 50);
            _notificationsList.Columns.Add("Título", 200);
            _notificationsList.Columns.Add("Mensagem", 300);
            _notificationsList.Columns.Add("Tipo", 80);
            _notificationsList.Columns.Add("Prioridade", 80);
            _notificationsList.Columns.Add("Lida", 60);
            _notificationsList.Columns.Add("Data", 120);

            _notificationsList.MouseDoubleClick += NotificationsList_MouseDoubleClick;

            this.Controls.Add(_notificationsList);
            this.Controls.Add(topPanel);
        }

        private void SetupSignalREvents()
        {
            _signalRService.NotificationReceived += OnNotificationReceived;
            _signalRService.UnreadCountUpdated += OnUnreadCountUpdated;
            _signalRService.NotificationRead += OnNotificationRead;
        }

        private async void ConnectButton_Click(object? sender, EventArgs e)
        {
            try
            {
                _currentUserId = _userIdTextBox.Text.Trim();
                if (string.IsNullOrEmpty(_currentUserId))
                {
                    MessageBox.Show("Por favor, insira um User ID válido.");
                    return;
                }

                _connectButton.Enabled = false;
                _connectButton.Text = "Conectando...";

                await _signalRService.StartConnectionAsync(_currentUserId);
                
                _connectButton.Text = "Conectado";
                _connectButton.BackColor = Color.LightGreen;

                await LoadNotifications();
                await UpdateUnreadCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao conectar: {ex.Message}");
                _connectButton.Enabled = true;
                _connectButton.Text = "Conectar";
            }
        }

        private async void RefreshButton_Click(object? sender, EventArgs e)
        {
            await LoadNotifications();
            await UpdateUnreadCount();
        }

        private async void MarkAllReadButton_Click(object? sender, EventArgs e)
        {
            await _apiClient.MarkAllAsReadAsync(_currentUserId);
            await LoadNotifications();
        }

        private void CreateNotificationButton_Click(object? sender, EventArgs e)
        {
            var createForm = new CreateNotificationForm(_apiClient, _currentUserId);
            if (createForm.ShowDialog() == DialogResult.OK)
            {
                // A notificação será recebida via SignalR
            }
        }

        private async void NotificationsList_MouseDoubleClick(object? sender, MouseEventArgs e)
        {
            if (_notificationsList.SelectedItems.Count > 0)
            {
                var item = _notificationsList.SelectedItems[0];
                var notificationId = int.Parse(item.Text);
                var isRead = item.SubItems[5].Text == "Sim";

                if (!isRead)
                {
                    await _apiClient.MarkAsReadAsync(notificationId, _currentUserId);
                }
            }
        }

        private void OnNotificationReceived(NotificationResponseDto notification)
        {
            if (InvokeRequired)
            {
                Invoke(() => OnNotificationReceived(notification));
                return;
            }

            // Adicionar à lista
            AddNotificationToList(notification);

            // Mostrar toast notification
            ShowToastNotification(notification);
        }

        private void OnUnreadCountUpdated(int count)
        {
            if (InvokeRequired)
            {
                Invoke(() => OnUnreadCountUpdated(count));
                return;
            }

            _unreadCountLabel.Text = $"Não lidas: {count}";
        }

        private void OnNotificationRead(int notificationId)
        {
            if (InvokeRequired)
            {
                Invoke(() => OnNotificationRead(notificationId));
                return;
            }

            // Atualizar item na lista
            foreach (ListViewItem item in _notificationsList.Items)
            {
                if (int.Parse(item.Text) == notificationId)
                {
                    item.SubItems[5].Text = "Sim";
                    item.BackColor = Color.LightGray;
                    break;
                }
            }
        }

        private async Task LoadNotifications()
        {
            try
            {
                var notifications = await _apiClient.GetRecentNotificationsAsync(_currentUserId, 50);
                
                _notificationsList.Items.Clear();
                
                foreach (var notification in notifications)
                {
                    AddNotificationToList(notification);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao carregar notificações: {ex.Message}");
            }
        }

        private void AddNotificationToList(NotificationResponseDto notification)
        {
            var item = new ListViewItem(notification.Id.ToString());
            item.SubItems.Add(notification.Title);
            item.SubItems.Add(notification.Message);
            item.SubItems.Add(notification.Type.ToString());
            item.SubItems.Add(notification.Priority.ToString());
            item.SubItems.Add(notification.IsRead ? "Sim" : "Não");
            item.SubItems.Add(notification.CreatedAt.ToString("dd/MM/yyyy HH:mm"));

            if (!notification.IsRead)
            {
                item.Font = new Font(item.Font, FontStyle.Bold);
                item.BackColor = Color.LightYellow;
            }
            else
            {
                item.BackColor = Color.LightGray;
            }

            // Cor por tipo
            switch (notification.Type)
            {
                case NotificationType.Error:
                    item.ForeColor = Color.Red;
                    break;
                case NotificationType.Warning:
                    item.ForeColor = Color.Orange;
                    break;
                case NotificationType.Success:
                    item.ForeColor = Color.Green;
                    break;
            }

            _notificationsList.Items.Insert(0, item);
        }

        private async Task UpdateUnreadCount()
        {
            try
            {
                var count = await _apiClient.GetUnreadCountAsync(_currentUserId);
                _unreadCountLabel.Text = $"Não lidas: {count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao atualizar contagem: {ex.Message}");
            }
        }

        private void ShowToastNotification(NotificationResponseDto notification)
        {
            // Implementação simples de toast - pode ser melhorada
            var message = $"{notification.Title}\n{notification.Message}";
            MessageBox.Show(message, "Nova Notificação", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _signalRService.StopConnectionAsync().Wait();
            _apiClient.Dispose();
            base.OnFormClosing(e);
        }
    }
}
