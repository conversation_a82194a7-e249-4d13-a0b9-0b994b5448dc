-- Script para criar banco de dados PostgreSQL
-- Execute este script como superuser (postgres)

-- Criar banco de dados
CREATE DATABASE "NotificationsDB"
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'Portuguese_Brazil.1252'
    LC_CTYPE = 'Portuguese_Brazil.1252'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- Conectar ao banco criado
\c "NotificationsDB";

-- Criar usuário específico para a aplicação (opcional)
CREATE USER notificationuser WITH PASSWORD 'notification123';

-- Conceder per<PERSON><PERSON><PERSON>es
GRANT ALL PRIVILEGES ON DATABASE "NotificationsDB" TO notificationuser;
GRANT ALL ON SCHEMA public TO notificationuser;

-- Come<PERSON><PERSON><PERSON>
COMMENT ON DATABASE "NotificationsDB" IS 'Banco de dados do Sistema de Notificações Kodiak';
