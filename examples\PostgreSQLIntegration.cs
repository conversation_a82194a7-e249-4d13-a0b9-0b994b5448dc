using System;
using System.Threading.Tasks;
using Npgsql;
using NotificationShared.DTOs;
using NotificationShared.Models;

namespace Examples
{
    /// <summary>
    /// Exemplo de integração direta com PostgreSQL
    /// Útil para sistemas que já usam PostgreSQL e querem integrar notificações
    /// </summary>
    public class PostgreSQLIntegration
    {
        private readonly string _connectionString;

        public PostgreSQLIntegration(string connectionString = "Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432")
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// Criar notificação diretamente no banco PostgreSQL
        /// </summary>
        public async Task<int> CreateNotificationDirectAsync(string title, string message, string userId, 
            NotificationType type = NotificationType.Info, NotificationPriority priority = NotificationPriority.Normal)
        {
            const string sql = @"
                INSERT INTO ""Notifications"" 
                (""Title"", ""Message"", ""UserId"", ""Type"", ""Priority"", ""CreatedAt"", ""IsRead"")
                VALUES (@title, @message, @userId, @type, @priority, NOW(), false)
                RETURNING ""Id""";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@title", title);
            command.Parameters.AddWithValue("@message", message);
            command.Parameters.AddWithValue("@userId", userId);
            command.Parameters.AddWithValue("@type", (int)type);
            command.Parameters.AddWithValue("@priority", (int)priority);

            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// Obter notificações não lidas de um usuário
        /// </summary>
        public async Task<List<NotificationInfo>> GetUnreadNotificationsAsync(string userId)
        {
            const string sql = @"
                SELECT ""Id"", ""Title"", ""Message"", ""Type"", ""Priority"", ""CreatedAt""
                FROM ""Notifications""
                WHERE ""UserId"" = @userId 
                  AND ""IsRead"" = false 
                  AND (""ExpiresAt"" IS NULL OR ""ExpiresAt"" > NOW())
                ORDER BY ""Priority"" DESC, ""CreatedAt"" DESC";

            var notifications = new List<NotificationInfo>();

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@userId", userId);

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                notifications.Add(new NotificationInfo
                {
                    Id = reader.GetInt32("Id"),
                    Title = reader.GetString("Title"),
                    Message = reader.GetString("Message"),
                    Type = (NotificationType)reader.GetInt32("Type"),
                    Priority = (NotificationPriority)reader.GetInt32("Priority"),
                    CreatedAt = reader.GetDateTime("CreatedAt")
                });
            }

            return notifications;
        }

        /// <summary>
        /// Marcar notificação como lida
        /// </summary>
        public async Task<bool> MarkAsReadAsync(int notificationId, string userId)
        {
            const string sql = @"
                UPDATE ""Notifications""
                SET ""IsRead"" = true, ""ReadAt"" = NOW()
                WHERE ""Id"" = @id AND ""UserId"" = @userId";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@id", notificationId);
            command.Parameters.AddWithValue("@userId", userId);

            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        /// <summary>
        /// Obter contagem de notificações não lidas
        /// </summary>
        public async Task<int> GetUnreadCountAsync(string userId)
        {
            const string sql = @"
                SELECT COUNT(*)
                FROM ""Notifications""
                WHERE ""UserId"" = @userId 
                  AND ""IsRead"" = false 
                  AND (""ExpiresAt"" IS NULL OR ""ExpiresAt"" > NOW())";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            command.Parameters.AddWithValue("@userId", userId);

            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        /// <summary>
        /// Limpar notificações expiradas
        /// </summary>
        public async Task<int> CleanupExpiredNotificationsAsync()
        {
            const string sql = @"
                DELETE FROM ""Notifications""
                WHERE ""ExpiresAt"" IS NOT NULL 
                  AND ""ExpiresAt"" < NOW()";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new NpgsqlCommand(sql, connection);
            var rowsAffected = await command.ExecuteNonQueryAsync();

            Console.WriteLine($"Removidas {rowsAffected} notificações expiradas.");
            return rowsAffected;
        }

        /// <summary>
        /// Exemplo de trigger PostgreSQL para auditoria
        /// </summary>
        public async Task CreateAuditTriggerAsync()
        {
            const string createAuditTable = @"
                CREATE TABLE IF NOT EXISTS ""NotificationAudit"" (
                    ""Id"" SERIAL PRIMARY KEY,
                    ""NotificationId"" INTEGER,
                    ""Action"" VARCHAR(10),
                    ""UserId"" VARCHAR(100),
                    ""Timestamp"" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    ""Details"" JSONB
                )";

            const string createTriggerFunction = @"
                CREATE OR REPLACE FUNCTION audit_notification_changes()
                RETURNS TRIGGER AS $$
                BEGIN
                    IF TG_OP = 'UPDATE' AND OLD.""IsRead"" = false AND NEW.""IsRead"" = true THEN
                        INSERT INTO ""NotificationAudit"" (""NotificationId"", ""Action"", ""UserId"", ""Details"")
                        VALUES (NEW.""Id"", 'READ', NEW.""UserId"", 
                               json_build_object('read_at', NEW.""ReadAt""));
                    END IF;
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql";

            const string createTrigger = @"
                DROP TRIGGER IF EXISTS notification_audit_trigger ON ""Notifications"";
                CREATE TRIGGER notification_audit_trigger
                    AFTER UPDATE ON ""Notifications""
                    FOR EACH ROW
                    EXECUTE FUNCTION audit_notification_changes()";

            using var connection = new NpgsqlConnection(_connectionString);
            await connection.OpenAsync();

            // Criar tabela de auditoria
            using var command1 = new NpgsqlCommand(createAuditTable, connection);
            await command1.ExecuteNonQueryAsync();

            // Criar função do trigger
            using var command2 = new NpgsqlCommand(createTriggerFunction, connection);
            await command2.ExecuteNonQueryAsync();

            // Criar trigger
            using var command3 = new NpgsqlCommand(createTrigger, connection);
            await command3.ExecuteNonQueryAsync();

            Console.WriteLine("Trigger de auditoria criado com sucesso!");
        }

        /// <summary>
        /// Exemplo de uso completo
        /// </summary>
        public async Task RunExampleAsync()
        {
            Console.WriteLine("=== Exemplo PostgreSQL Integration ===\n");

            try
            {
                // 1. Criar notificação
                Console.WriteLine("1. Criando notificação...");
                var notificationId = await CreateNotificationDirectAsync(
                    "Integração PostgreSQL", 
                    "Exemplo de integração direta com PostgreSQL", 
                    "user_********",
                    NotificationType.Success,
                    NotificationPriority.High);
                Console.WriteLine($"Notificação criada com ID: {notificationId}\n");

                // 2. Obter não lidas
                Console.WriteLine("2. Obtendo notificações não lidas...");
                var unreadNotifications = await GetUnreadNotificationsAsync("user_********");
                Console.WriteLine($"Encontradas {unreadNotifications.Count} notificações não lidas:");
                foreach (var notif in unreadNotifications)
                {
                    Console.WriteLine($"  - {notif.Title} ({notif.Type}) - {notif.CreatedAt:dd/MM/yyyy HH:mm}");
                }
                Console.WriteLine();

                // 3. Contagem não lidas
                Console.WriteLine("3. Contagem de não lidas...");
                var count = await GetUnreadCountAsync("user_********");
                Console.WriteLine($"Total não lidas: {count}\n");

                // 4. Marcar como lida
                Console.WriteLine("4. Marcando como lida...");
                var marked = await MarkAsReadAsync(notificationId, "user_********");
                Console.WriteLine($"Marcada como lida: {marked}\n");

                // 5. Verificar contagem novamente
                Console.WriteLine("5. Nova contagem...");
                count = await GetUnreadCountAsync("user_********");
                Console.WriteLine($"Total não lidas após marcar: {count}\n");

                // 6. Criar trigger de auditoria
                Console.WriteLine("6. Criando trigger de auditoria...");
                await CreateAuditTriggerAsync();

                // 7. Limpeza
                Console.WriteLine("7. Limpando notificações expiradas...");
                await CleanupExpiredNotificationsAsync();

                Console.WriteLine("Exemplo concluído com sucesso!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Classe para representar informações básicas da notificação
    /// </summary>
    public class NotificationInfo
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public NotificationPriority Priority { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// Programa de exemplo
    /// </summary>
    class PostgreSQLProgram
    {
        static async Task Main(string[] args)
        {
            var integration = new PostgreSQLIntegration();
            await integration.RunExampleAsync();

            Console.WriteLine("\nPressione qualquer tecla para sair...");
            Console.ReadKey();
        }
    }
}
