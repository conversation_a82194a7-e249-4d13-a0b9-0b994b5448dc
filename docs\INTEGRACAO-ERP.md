# Integração com Sistema ERP - Guia Passo a Passo

## 📋 Visão Geral da Integração

Este guia mostra como integrar o sistema de notificações no seu ERP existente que usa PostgreSQL.

### Cenários de Integração:
1. **API REST** - Chamadas HTTP para criar/gerenciar notificações
2. **Integração Direta no Banco** - Inserir notificações diretamente no PostgreSQL
3. **SignalR Client** - Receber notificações em tempo real no ERP
4. **Triggers PostgreSQL** - Notificações automáticas baseadas em eventos do ERP

## 🚀 Passo 1: Preparação do Ambiente

### 1.1 Instalar o Sistema de Notificações
```bash
# 1. Clone o repositório
git clone <repo-url>
cd NotificacoesKodiak

# 2. Configure PostgreSQL (use seu banco existente ou crie um novo)
scripts/setup-postgresql.bat

# 3. Compile e teste
scripts/build-all.bat
scripts/start-api-postgresql.bat
```

### 1.2 Configurar Connection String
Edite `src/NotificationAPI/appsettings.Development.json`:
```json
{
  "DatabaseProvider": "PostgreSQL",
  "ConnectionStrings": {
    "DefaultConnection": "Host=SEU_HOST;Database=SEU_BANCO_ERP;Username=SEU_USER;Password=*********;Port=5432"
  }
}
```

### 1.3 Criar Tabela no Banco do ERP
Execute no seu banco PostgreSQL:
```sql
-- Conectar ao seu banco ERP
\c "SeuBancoERP"

-- Criar tabela de notificações
CREATE TABLE IF NOT EXISTS "Notifications" (
    "Id" SERIAL PRIMARY KEY,
    "Title" VARCHAR(200) NOT NULL,
    "Message" VARCHAR(1000) NOT NULL,
    "UserId" VARCHAR(100) NOT NULL,
    "Type" INTEGER NOT NULL DEFAULT 0,
    "Priority" INTEGER NOT NULL DEFAULT 1,
    "IsRead" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "ReadAt" TIMESTAMP WITH TIME ZONE NULL,
    "ExpiresAt" TIMESTAMP WITH TIME ZONE NULL,
    "ActionUrl" VARCHAR(500) NULL,
    "ActionText" VARCHAR(100) NULL,
    "Metadata" TEXT NULL
);

-- Criar índices
CREATE INDEX IF NOT EXISTS "IX_Notifications_UserId" ON "Notifications" ("UserId");
CREATE INDEX IF NOT EXISTS "IX_Notifications_IsRead" ON "Notifications" ("IsRead");
CREATE INDEX IF NOT EXISTS "IX_Notifications_UserId_IsRead" ON "Notifications" ("UserId", "IsRead");
```

## 🔧 Passo 2: Integração via API REST

### 2.1 Criar Cliente HTTP no ERP
```csharp
// Adicione ao seu projeto ERP
public class NotificationService
{
    private readonly HttpClient _httpClient;
    private readonly string _apiBaseUrl;

    public NotificationService(string apiBaseUrl = "https://localhost:7001")
    {
        _httpClient = new HttpClient();
        _apiBaseUrl = apiBaseUrl;
    }

    public async Task<bool> EnviarNotificacaoAsync(string titulo, string mensagem, string userId, 
        string tipo = "Info", string prioridade = "Normal")
    {
        try
        {
            var notification = new
            {
                title = titulo,
                message = mensagem,
                userId = userId,
                type = tipo,
                priority = prioridade
            };

            var json = JsonConvert.SerializeObject(notification);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_apiBaseUrl}/api/notifications", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            // Log do erro
            Console.WriteLine($"Erro ao enviar notificação: {ex.Message}");
            return false;
        }
    }

    public async Task<int> ObterContadorNaoLidasAsync(string userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_apiBaseUrl}/api/notifications/unread-count?userId={userId}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<int>(json);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erro ao obter contador: {ex.Message}");
        }
        return 0;
    }
}
```

### 2.2 Usar no ERP
```csharp
// Exemplo de uso no seu ERP
public class VendaService
{
    private readonly NotificationService _notificationService;

    public VendaService()
    {
        _notificationService = new NotificationService();
    }

    public async Task ProcessarVenda(Venda venda)
    {
        // Sua lógica de venda existente
        // ...

        // Enviar notificação
        await _notificationService.EnviarNotificacaoAsync(
            titulo: "Nova Venda Realizada",
            mensagem: $"Venda #{venda.Id} no valor de R$ {venda.Valor:F2} foi processada com sucesso.",
            userId: venda.VendedorId,
            tipo: "Success",
            prioridade: "Normal"
        );

        // Notificar supervisor se valor alto
        if (venda.Valor > 10000)
        {
            await _notificationService.EnviarNotificacaoAsync(
                titulo: "Venda de Alto Valor",
                mensagem: $"Venda #{venda.Id} de R$ {venda.Valor:F2} requer aprovação.",
                userId: venda.SupervisorId,
                tipo: "Warning",
                prioridade: "High"
            );
        }
    }
}
```

## 🗄️ Passo 3: Integração Direta no Banco

### 3.1 Criar Classe de Integração Direta
```csharp
// Adicione ao seu projeto ERP
public class NotificationDirectService
{
    private readonly string _connectionString;

    public NotificationDirectService(string connectionString)
    {
        _connectionString = connectionString;
    }

    public async Task<int> CriarNotificacaoAsync(string titulo, string mensagem, string userId, 
        int tipo = 0, int prioridade = 1, DateTime? expiraEm = null)
    {
        const string sql = @"
            INSERT INTO ""Notifications"" 
            (""Title"", ""Message"", ""UserId"", ""Type"", ""Priority"", ""ExpiresAt"", ""CreatedAt"", ""IsRead"")
            VALUES (@titulo, @mensagem, @userId, @tipo, @prioridade, @expiraEm, NOW(), false)
            RETURNING ""Id""";

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync();

        using var command = new NpgsqlCommand(sql, connection);
        command.Parameters.AddWithValue("@titulo", titulo);
        command.Parameters.AddWithValue("@mensagem", mensagem);
        command.Parameters.AddWithValue("@userId", userId);
        command.Parameters.AddWithValue("@tipo", tipo);
        command.Parameters.AddWithValue("@prioridade", prioridade);
        command.Parameters.AddWithValue("@expiraEm", (object)expiraEm ?? DBNull.Value);

        var result = await command.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }

    public async Task<List<NotificacaoInfo>> ObterNotificacoesUsuarioAsync(string userId, bool? apenasNaoLidas = null)
    {
        var sql = @"
            SELECT ""Id"", ""Title"", ""Message"", ""Type"", ""Priority"", ""IsRead"", ""CreatedAt""
            FROM ""Notifications""
            WHERE ""UserId"" = @userId";

        if (apenasNaoLidas.HasValue)
            sql += " AND \"IsRead\" = @isRead";

        sql += " ORDER BY \"Priority\" DESC, \"CreatedAt\" DESC LIMIT 50";

        var notificacoes = new List<NotificacaoInfo>();

        using var connection = new NpgsqlConnection(_connectionString);
        await connection.OpenAsync();

        using var command = new NpgsqlCommand(sql, connection);
        command.Parameters.AddWithValue("@userId", userId);
        if (apenasNaoLidas.HasValue)
            command.Parameters.AddWithValue("@isRead", !apenasNaoLidas.Value);

        using var reader = await command.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            notificacoes.Add(new NotificacaoInfo
            {
                Id = reader.GetInt32("Id"),
                Titulo = reader.GetString("Title"),
                Mensagem = reader.GetString("Message"),
                Tipo = reader.GetInt32("Type"),
                Prioridade = reader.GetInt32("Priority"),
                Lida = reader.GetBoolean("IsRead"),
                CriadaEm = reader.GetDateTime("CreatedAt")
            });
        }

        return notificacoes;
    }
}

public class NotificacaoInfo
{
    public int Id { get; set; }
    public string Titulo { get; set; } = string.Empty;
    public string Mensagem { get; set; } = string.Empty;
    public int Tipo { get; set; }
    public int Prioridade { get; set; }
    public bool Lida { get; set; }
    public DateTime CriadaEm { get; set; }
}
```

## 🔔 Passo 4: Triggers Automáticos no PostgreSQL

### 4.1 Criar Triggers para Eventos do ERP
```sql
-- Exemplo: Notificação automática quando estoque baixo
CREATE OR REPLACE FUNCTION notificar_estoque_baixo()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.quantidade <= NEW.estoque_minimo AND OLD.quantidade > OLD.estoque_minimo THEN
        INSERT INTO "Notifications" ("Title", "Message", "UserId", "Type", "Priority", "CreatedAt")
        VALUES (
            'Estoque Baixo',
            'Produto ' || NEW.nome || ' está com estoque baixo (' || NEW.quantidade || ' unidades)',
            'gerente_estoque',
            2, -- Warning
            2, -- High
            NOW()
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger
CREATE TRIGGER trigger_estoque_baixo
    AFTER UPDATE ON produtos
    FOR EACH ROW
    EXECUTE FUNCTION notificar_estoque_baixo();
```

### 4.2 Trigger para Vendas Importantes
```sql
-- Notificação automática para vendas acima de um valor
CREATE OR REPLACE FUNCTION notificar_venda_importante()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.valor_total > 5000 THEN
        INSERT INTO "Notifications" ("Title", "Message", "UserId", "Type", "Priority", "CreatedAt")
        VALUES (
            'Venda Importante',
            'Nova venda #' || NEW.id || ' no valor de R$ ' || NEW.valor_total || ' realizada por ' || NEW.vendedor_nome,
            'supervisor_vendas',
            1, -- Success
            2, -- High
            NOW()
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_venda_importante
    AFTER INSERT ON vendas
    FOR EACH ROW
    EXECUTE FUNCTION notificar_venda_importante();
```

## 📱 Passo 5: Interface no ERP (Windows Forms)

### 5.1 Adicionar Painel de Notificações
```csharp
// Adicione ao seu formulário principal do ERP
public partial class FormPrincipalERP : Form
{
    private readonly NotificationDirectService _notificationService;
    private readonly Timer _timerNotificacoes;
    private Label _labelContadorNotificacoes;
    private ListBox _listBoxNotificacoes;

    public FormPrincipalERP()
    {
        InitializeComponent();
        _notificationService = new NotificationDirectService("sua_connection_string");
        
        ConfigurarPainelNotificacoes();
        IniciarTimerNotificacoes();
    }

    private void ConfigurarPainelNotificacoes()
    {
        // Contador de notificações não lidas
        _labelContadorNotificacoes = new Label
        {
            Text = "Notificações: 0",
            Location = new Point(10, 10),
            Size = new Size(150, 23),
            Font = new Font("Arial", 10, FontStyle.Bold),
            ForeColor = Color.Red
        };

        // Lista de notificações
        _listBoxNotificacoes = new ListBox
        {
            Location = new Point(10, 40),
            Size = new Size(400, 200),
            Visible = false
        };

        _labelContadorNotificacoes.Click += MostrarOcultarNotificacoes;
        _listBoxNotificacoes.DoubleClick += MarcarNotificacaoComoLida;

        this.Controls.Add(_labelContadorNotificacoes);
        this.Controls.Add(_listBoxNotificacoes);
    }

    private void IniciarTimerNotificacoes()
    {
        _timerNotificacoes = new Timer();
        _timerNotificacoes.Interval = 30000; // 30 segundos
        _timerNotificacoes.Tick += async (s, e) => await AtualizarNotificacoes();
        _timerNotificacoes.Start();

        // Carregar notificações iniciais
        Task.Run(async () => await AtualizarNotificacoes());
    }

    private async Task AtualizarNotificacoes()
    {
        try
        {
            var usuarioAtual = ObterUsuarioLogado(); // Sua lógica para obter usuário
            var notificacoes = await _notificationService.ObterNotificacoesUsuarioAsync(usuarioAtual, apenasNaoLidas: true);

            if (InvokeRequired)
            {
                Invoke(new Action(() => AtualizarInterface(notificacoes)));
            }
            else
            {
                AtualizarInterface(notificacoes);
            }
        }
        catch (Exception ex)
        {
            // Log do erro
            Console.WriteLine($"Erro ao atualizar notificações: {ex.Message}");
        }
    }

    private void AtualizarInterface(List<NotificacaoInfo> notificacoes)
    {
        var naoLidas = notificacoes.Count;
        _labelContadorNotificacoes.Text = $"Notificações: {naoLidas}";
        _labelContadorNotificacoes.ForeColor = naoLidas > 0 ? Color.Red : Color.Green;

        _listBoxNotificacoes.Items.Clear();
        foreach (var notif in notificacoes.Take(10))
        {
            _listBoxNotificacoes.Items.Add($"{notif.Titulo} - {notif.CriadaEm:HH:mm}");
        }

        // Mostrar toast para novas notificações
        if (naoLidas > 0)
        {
            MostrarToastNotificacao(notificacoes.First());
        }
    }

    private void MostrarOcultarNotificacoes(object sender, EventArgs e)
    {
        _listBoxNotificacoes.Visible = !_listBoxNotificacoes.Visible;
    }

    private async void MarcarNotificacaoComoLida(object sender, EventArgs e)
    {
        if (_listBoxNotificacoes.SelectedIndex >= 0)
        {
            // Implementar lógica para marcar como lida
            await AtualizarNotificacoes();
        }
    }

    private void MostrarToastNotificacao(NotificacaoInfo notificacao)
    {
        // Toast simples - você pode usar bibliotecas mais sofisticadas
        MessageBox.Show(
            $"{notificacao.Titulo}\n\n{notificacao.Mensagem}",
            "Nova Notificação",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information
        );
    }

    private string ObterUsuarioLogado()
    {
        // Sua lógica para obter o usuário logado no ERP
        return "usuario_atual";
    }
}
```

## 🎯 Passo 6: Casos de Uso Práticos

### 6.1 Notificações de Vendas
```csharp
// No seu módulo de vendas
public async Task ProcessarVenda(Venda venda)
{
    // Processar venda...
    
    // Notificar vendedor
    await _notificationService.CriarNotificacaoAsync(
        "Venda Processada",
        $"Sua venda #{venda.Id} foi processada com sucesso!",
        venda.VendedorId,
        tipo: 1 // Success
    );

    // Notificar financeiro se valor alto
    if (venda.Valor > 10000)
    {
        await _notificationService.CriarNotificacaoAsync(
            "Venda Alto Valor",
            $"Venda #{venda.Id} de R$ {venda.Valor:F2} precisa de aprovação financeira.",
            "financeiro",
            tipo: 2, // Warning
            prioridade: 2 // High
        );
    }
}
```

### 6.2 Notificações de Estoque
```csharp
// No seu módulo de estoque
public async Task VerificarEstoque(int produtoId)
{
    var produto = ObterProduto(produtoId);
    
    if (produto.Quantidade <= produto.EstoqueMinimo)
    {
        await _notificationService.CriarNotificacaoAsync(
            "Estoque Baixo",
            $"Produto {produto.Nome} está com apenas {produto.Quantidade} unidades em estoque.",
            "gerente_estoque",
            tipo: 2, // Warning
            prioridade: 2 // High
        );
    }
}
```

### 6.3 Notificações de Cobrança
```csharp
// No seu módulo financeiro
public async Task VerificarContasVencidas()
{
    var contasVencidas = ObterContasVencidas();
    
    foreach (var conta in contasVencidas)
    {
        await _notificationService.CriarNotificacaoAsync(
            "Conta Vencida",
            $"Conta #{conta.Id} do cliente {conta.ClienteNome} venceu em {conta.DataVencimento:dd/MM/yyyy}",
            "cobranca",
            tipo: 3, // Error
            prioridade: 2 // High
        );
    }
}
```

## ✅ Passo 7: Checklist de Implementação

### Preparação:
- [ ] PostgreSQL instalado e configurado
- [ ] Sistema de notificações compilado e testado
- [ ] Tabela criada no banco do ERP
- [ ] Connection string configurada

### Integração:
- [ ] Cliente HTTP implementado no ERP
- [ ] Serviço de integração direta criado
- [ ] Triggers PostgreSQL configurados (opcional)
- [ ] Interface de notificações adicionada ao ERP

### Testes:
- [ ] Teste de criação de notificação via API
- [ ] Teste de integração direta no banco
- [ ] Teste de triggers automáticos
- [ ] Teste de interface no ERP

### Produção:
- [ ] Configuração de produção ajustada
- [ ] Logs e monitoramento configurados
- [ ] Backup da tabela de notificações
- [ ] Documentação para usuários

## 🔧 Próximos Passos

1. **Teste a integração** com dados reais do seu ERP
2. **Configure triggers** para eventos específicos do seu negócio
3. **Personalize a interface** conforme seu layout atual
4. **Implemente em produção** gradualmente

Precisa de ajuda com algum passo específico ou tem dúvidas sobre a integração?
