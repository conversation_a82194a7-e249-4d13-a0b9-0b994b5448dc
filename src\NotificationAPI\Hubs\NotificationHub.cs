using Microsoft.AspNetCore.SignalR;
using NotificationShared.DTOs;

namespace NotificationAPI.Hubs
{
    public class NotificationHub : Hub
    {
        private static readonly Dictionary<string, string> UserConnections = new();

        public async Task JoinUserGroup(string userId)
        {
            UserConnections[Context.ConnectionId] = userId;
            await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
            await Clients.Caller.SendAsync("JoinedGroup", $"User_{userId}");
        }

        public async Task LeaveUserGroup(string userId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
            UserConnections.Remove(Context.ConnectionId);
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            if (UserConnections.TryGetValue(Context.ConnectionId, out var userId))
            {
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"User_{userId}");
                UserConnections.Remove(Context.ConnectionId);
            }
            await base.OnDisconnectedAsync(exception);
        }

        // Método para enviar notificação para um usuário específico
        public async Task SendNotificationToUser(string userId, NotificationResponseDto notification)
        {
            await Clients.Group($"User_{userId}").SendAsync("ReceiveNotification", notification);
        }

        // Método para enviar atualização de contagem de não lidas
        public async Task SendUnreadCountUpdate(string userId, int count)
        {
            await Clients.Group($"User_{userId}").SendAsync("UnreadCountUpdated", count);
        }

        // Método para notificar que uma notificação foi marcada como lida
        public async Task SendNotificationRead(string userId, int notificationId)
        {
            await Clients.Group($"User_{userId}").SendAsync("NotificationRead", notificationId);
        }
    }

    // Interface para usar o hub em outros serviços
    public interface INotificationHubService
    {
        Task SendNotificationToUserAsync(string userId, NotificationResponseDto notification);
        Task SendUnreadCountUpdateAsync(string userId, int count);
        Task SendNotificationReadAsync(string userId, int notificationId);
    }

    public class NotificationHubService : INotificationHubService
    {
        private readonly IHubContext<NotificationHub> _hubContext;

        public NotificationHubService(IHubContext<NotificationHub> hubContext)
        {
            _hubContext = hubContext;
        }

        public async Task SendNotificationToUserAsync(string userId, NotificationResponseDto notification)
        {
            await _hubContext.Clients.Group($"User_{userId}").SendAsync("ReceiveNotification", notification);
        }

        public async Task SendUnreadCountUpdateAsync(string userId, int count)
        {
            await _hubContext.Clients.Group($"User_{userId}").SendAsync("UnreadCountUpdated", count);
        }

        public async Task SendNotificationReadAsync(string userId, int notificationId)
        {
            await _hubContext.Clients.Group($"User_{userId}").SendAsync("NotificationRead", notificationId);
        }
    }
}
