@echo off
echo ========================================
echo  Setup PostgreSQL para Sistema Kodiak
echo ========================================
echo.

echo Verificando se o PostgreSQL esta instalado...
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: PostgreSQL nao encontrado!
    echo Por favor, instale o PostgreSQL primeiro.
    echo Download: https://www.postgresql.org/download/windows/
    pause
    exit /b 1
)

echo PostgreSQL encontrado!
echo.

echo Criando banco de dados...
echo Digite a senha do usuario postgres quando solicitado:
psql -U postgres -f "%~dp0\..\database\postgresql\create-database.sql"

if %errorlevel% neq 0 (
    echo ERRO: Falha ao criar banco de dados!
    pause
    exit /b 1
)

echo.
echo Criando tabelas...
psql -U postgres -d NotificationsDB -f "%~dp0\..\database\postgresql\create-tables.sql"

if %errorlevel% neq 0 (
    echo ERRO: Falha ao criar tabelas!
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Setup concluido com sucesso!
echo ========================================
echo.
echo Banco de dados: NotificationsDB
echo Host: localhost
echo Porta: 5432
echo Usuario: postgres
echo.
echo Para usar o PostgreSQL, edite o arquivo:
echo src\NotificationAPI\appsettings.Development.json
echo.
echo E configure:
echo "DatabaseProvider": "PostgreSQL"
echo.
pause
