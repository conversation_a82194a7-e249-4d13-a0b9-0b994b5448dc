using NotificationShared.DTOs;
using NotificationShared.Models;

namespace NotificationAPI.Services
{
    public interface INotificationService
    {
        Task<NotificationResponseDto> CreateNotificationAsync(CreateNotificationDto dto);
        Task<NotificationResponseDto?> GetNotificationByIdAsync(int id);
        Task<IEnumerable<NotificationResponseDto>> GetNotificationsAsync(NotificationQueryDto query);
        Task<NotificationResponseDto?> UpdateNotificationAsync(int id, UpdateNotificationDto dto);
        Task<bool> DeleteNotificationAsync(int id);
        Task<bool> MarkAsReadAsync(int id, string userId);
        Task<bool> MarkAsUnreadAsync(int id, string userId);
        Task<int> GetUnreadCountAsync(string userId);
        Task<bool> MarkAllAsReadAsync(string userId);
        Task<IEnumerable<NotificationResponseDto>> GetRecentNotificationsAsync(string userId, int count = 10);
    }
}
