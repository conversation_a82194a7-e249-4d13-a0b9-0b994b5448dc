# Configuração PostgreSQL - Sistema de Notificações Kodiak

## Pré-requisitos

### 1. Instalar PostgreSQL
- **Windows**: [Download PostgreSQL](https://www.********ql.org/download/windows/)
- **Linux**: `sudo apt-get install ********ql ********ql-contrib`
- **macOS**: `brew install ********ql`

### 2. Verificar Instalação
```bash
psql --version
```

## Configuração Rápida

### Método 1: Script Automatizado (Windows)
```bash
scripts/setup-********ql.bat
```

### Método 2: Manual

#### 1. <PERSON><PERSON><PERSON><PERSON>
```sql
-- Conectar como ********
psql -U ********

-- Criar banco
CREATE DATABASE "NotificationsDB"
    WITH 
    OWNER = ********
    ENCODING = 'UTF8';

-- Sair
\q
```

#### 2. Executar Scripts
```bash
# Criar estrutura
psql -U ******** -d NotificationsDB -f database/********ql/create-tables.sql
```

#### 3. Configurar API
Edite `src/NotificationAPI/appsettings.Development.json`:
```json
{
  "DatabaseProvider": "PostgreSQL",
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotificationsDB;Username=********;Password=*********;Port=5432"
  }
}
```

## Configurações de Conexão

### Desenvolvimento Local
```json
{
  "DatabaseProvider": "PostgreSQL",
  "ConnectionStrings": {
    "PostgreSQLConnection": "Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432"
  }
}
```

### Produção
```json
{
  "DatabaseProvider": "PostgreSQL",
  "ConnectionStrings": {
    "PostgreSQLConnection": "Host=seu-servidor;Database=NotificationsDB;Username=app_user;Password=************;Port=5432;SSL Mode=Require"
  }
}
```

### Usando Variáveis de Ambiente
```bash
# Windows
set DB_HOST=localhost
set DB_NAME=NotificationsDB
set DB_USER=********
set DB_PASSWORD=********
set DB_PORT=5432

# Linux/macOS
export DB_HOST=localhost
export DB_NAME=NotificationsDB
export DB_USER=********
export DB_PASSWORD=********
export DB_PORT=5432
```

## Executar com PostgreSQL

### 1. Iniciar API
```bash
scripts/start-api-********ql.bat
```

### 2. Verificar Conexão
- Acesse: `https://localhost:7001/swagger`
- Teste endpoint: `GET /api/notifications/unread-count?userId=user1`

## Migrações Entity Framework

### Criar Migração
```bash
cd src/NotificationAPI
dotnet ef migrations add InitialCreate --context NotificationContext
```

### Aplicar Migração
```bash
dotnet ef database update --context NotificationContext
```

### Reverter Migração
```bash
dotnet ef database update PreviousMigration --context NotificationContext
```

## Comandos Úteis PostgreSQL

### Conectar ao Banco
```bash
psql -U ******** -d NotificationsDB
```

### Listar Tabelas
```sql
\dt
```

### Descrever Tabela
```sql
\d "Notifications"
```

### Ver Dados
```sql
SELECT * FROM "Notifications" ORDER BY "CreatedAt" DESC LIMIT 10;
```

### Limpar Dados
```sql
TRUNCATE TABLE "Notifications" RESTART IDENTITY;
```

### Backup
```bash
pg_dump -U ******** -d NotificationsDB > backup.sql
```

### Restore
```bash
psql -U ******** -d NotificationsDB < backup.sql
```

## Performance e Otimização

### Índices Recomendados
```sql
-- Já criados automaticamente
CREATE INDEX IF NOT EXISTS "IX_Notifications_UserId_IsRead" ON "Notifications" ("UserId", "IsRead");
CREATE INDEX IF NOT EXISTS "IX_Notifications_CreatedAt" ON "Notifications" ("CreatedAt");
CREATE INDEX IF NOT EXISTS "IX_Notifications_ExpiresAt" ON "Notifications" ("ExpiresAt");
```

### Configurações PostgreSQL
Edite `********ql.conf`:
```ini
# Memória
shared_buffers = 256MB
effective_cache_size = 1GB

# Conexões
max_connections = 100

# Logging
log_statement = 'mod'
log_min_duration_statement = 1000
```

### Monitoramento
```sql
-- Conexões ativas
SELECT * FROM pg_stat_activity WHERE datname = 'NotificationsDB';

-- Tamanho do banco
SELECT pg_size_pretty(pg_database_size('NotificationsDB'));

-- Estatísticas da tabela
SELECT * FROM pg_stat_user_tables WHERE relname = 'Notifications';
```

## Solução de Problemas

### Erro: "password authentication failed"
1. Verifique a senha do PostgreSQL
2. Edite `pg_hba.conf` se necessário
3. Reinicie o serviço PostgreSQL

### Erro: "database does not exist"
```bash
# Criar banco manualmente
createdb -U ******** NotificationsDB
```

### Erro: "connection refused"
1. Verifique se PostgreSQL está rodando:
   ```bash
   # Windows
   net start ********ql-x64-14
   
   # Linux
   sudo systemctl start ********ql
   ```

### Erro: "permission denied"
```sql
-- Conceder permissões
GRANT ALL PRIVILEGES ON DATABASE "NotificationsDB" TO ********;
GRANT ALL ON SCHEMA public TO ********;
```

## Configuração Docker (Opcional)

### docker-compose.yml
```yaml
version: '3.8'
services:
  ********:
    image: ********:15
    environment:
      POSTGRES_DB: NotificationsDB
      POSTGRES_USER: ********
      POSTGRES_PASSWORD: ********
    ports:
      - "5432:5432"
    volumes:
      - ********_data:/var/lib/********ql/data
      - ./database/********ql:/docker-entrypoint-initdb.d

volumes:
  ********_data:
```

### Executar
```bash
docker-compose up -d
```

## Segurança

### Usuário Específico da Aplicação
```sql
-- Criar usuário
CREATE USER notificationapp WITH PASSWORD '***************';

-- Conceder permissões mínimas
GRANT CONNECT ON DATABASE "NotificationsDB" TO notificationapp;
GRANT USAGE ON SCHEMA public TO notificationapp;
GRANT SELECT, INSERT, UPDATE, DELETE ON "Notifications" TO notificationapp;
GRANT USAGE, SELECT ON SEQUENCE "Notifications_Id_seq" TO notificationapp;
```

### String de Conexão Segura
```json
{
  "PostgreSQLConnection": "Host=localhost;Database=NotificationsDB;Username=notificationapp;Password=***************;Port=5432;SSL Mode=Require;Trust Server Certificate=false"
}
```

## Backup Automatizado

### Script de Backup (Windows)
```batch
@echo off
set BACKUP_DIR=C:\Backups\NotificationsDB
set DATE=%date:~-4,4%%date:~-10,2%%date:~-7,2%
set TIME=%time:~0,2%%time:~3,2%%time:~6,2%
set FILENAME=NotificationsDB_%DATE%_%TIME%.sql

mkdir "%BACKUP_DIR%" 2>nul
pg_dump -U ******** -d NotificationsDB > "%BACKUP_DIR%\%FILENAME%"

echo Backup criado: %FILENAME%
```

### Agendar no Windows
```bash
# Executar como Administrador
schtasks /create /tn "Backup NotificationsDB" /tr "C:\path\to\backup.bat" /sc daily /st 02:00
```
