# Sistema de Notificações Kodiak

Sistema completo de notificações para aplicações Windows Forms com API REST e comunicação em tempo real.

## 🚀 Início Rápido

### Pré-requisitos
- .NET 8.0 SDK
- Visual Studio 2022 ou VS Code
- **PostgreSQL** (recomendado) ou SQL Server ou SQLite

### Executar o Sistema

1. **Clone o repositório**

2. **Configure o banco PostgreSQL:**
   ```bash
   scripts/setup-********ql.bat
   ```
   📖 [Guia completo PostgreSQL](docs/POSTGRESQL-SETUP.md)

3. **Compile todos os projetos:**
   ```bash
   scripts/build-all.bat
   ```

4. **Inicie a API:**
   ```bash
   # Com PostgreSQL
   scripts/start-api-********ql.bat

   # Ou com SQLite (padrão)
   scripts/start-api.bat
   ```
   A API estará disponível em: `https://localhost:7001`
   Swagger UI: `https://localhost:7001/swagger`

5. **Inicie o Cliente Windows Forms:**
   ```bash
   scripts/start-client.bat
   ```

## 📁 Estrutura do Projeto

```
NotificacoesKodiak/
├── src/
│   ├── NotificationAPI/          # API ASP.NET Core
│   ├── NotificationClient/       # Cliente Windows Forms
│   └── NotificationShared/       # Modelos compartilhados
├── scripts/                      # Scripts de automação
├── examples/                     # Exemplos de uso
└── docs/                        # Documentação
```

## 🔧 Componentes

### 1. API de Notificações (ASP.NET Core)
- **Endpoints REST** para CRUD de notificações
- **SignalR Hub** para comunicação em tempo real
- **Entity Framework** para persistência
- **Swagger** para documentação da API
- **SQLite/SQL Server** como banco de dados

### 2. Cliente Windows Forms
- **Interface gráfica** para exibir notificações
- **Consumo da API REST** para operações CRUD
- **Conexão SignalR** para updates em tempo real
- **Sistema de notificações toast**
- **Filtros e busca** de notificações

### 3. Modelos Compartilhados
- **DTOs** para transferência de dados
- **Enums** para tipos e prioridades
- **Modelos de domínio** compartilhados

## ✨ Funcionalidades

- ✅ **Criar notificações** via API ou interface
- ✅ **Listar notificações** por usuário com filtros
- ✅ **Marcar como lida/não lida** individual ou em lote
- ✅ **Notificações em tempo real** via SignalR
- ✅ **Interface Windows Forms** intuitiva
- ✅ **Persistência em banco de dados** com SQLite/SQL Server
- ✅ **Diferentes tipos** (Info, Success, Warning, Error, System)
- ✅ **Níveis de prioridade** (Low, Normal, High, Critical)
- ✅ **Expiração de notificações** configurável
- ✅ **Actions personalizadas** com URLs e textos
- ✅ **Contagem de não lidas** em tempo real

## 🛠️ Tecnologias

- **Backend**: ASP.NET Core 8.0, Entity Framework Core, SignalR
- **Frontend**: Windows Forms (.NET 8.0)
- **Banco**: **PostgreSQL** (recomendado), SQL Server, SQLite
- **Comunicação**: REST API + SignalR WebSockets
- **Serialização**: Newtonsoft.Json

## 📖 Como Usar

### API Endpoints

#### Criar Notificação
```http
POST /api/notifications
Content-Type: application/json

{
  "title": "Título da Notificação",
  "message": "Mensagem detalhada",
  "userId": "user123",
  "type": "Info",
  "priority": "Normal",
  "expiresAt": "2024-12-31T23:59:59Z",
  "actionUrl": "https://example.com/action",
  "actionText": "Ver Detalhes"
}
```

#### Listar Notificações
```http
GET /api/notifications?userId=user123&isRead=false&page=1&pageSize=20
```

#### Marcar como Lida
```http
POST /api/notifications/{id}/mark-read?userId=user123
```

#### Obter Contagem de Não Lidas
```http
GET /api/notifications/unread-count?userId=user123
```

### Cliente Windows Forms

1. **Conectar**: Insira seu User ID e clique em "Conectar"
2. **Visualizar**: As notificações aparecerão na lista principal
3. **Marcar como lida**: Duplo-clique em uma notificação não lida
4. **Criar nova**: Clique em "Criar Notificação" para abrir o formulário
5. **Atualizar**: Use "Atualizar" para sincronizar manualmente

### Integração Programática

```csharp
// Exemplo de uso da API
var apiClient = new NotificationApiClient("https://localhost:7001");

// Criar notificação
var notification = new CreateNotificationDto
{
    Title = "Nova Mensagem",
    Message = "Você tem uma nova mensagem",
    UserId = "user123",
    Type = NotificationType.Info,
    Priority = NotificationPriority.Normal
};

var result = await apiClient.CreateNotificationAsync(notification);

// Listar notificações
var notifications = await apiClient.GetNotificationsAsync("user123");

// Marcar como lida
await apiClient.MarkAsReadAsync(notificationId, "user123");
```

## 🔧 Configuração

### Banco de Dados

#### PostgreSQL (Recomendado)
```json
{
  "DatabaseProvider": "PostgreSQL",
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432"
  }
}
```

#### SQL Server
```json
{
  "DatabaseProvider": "SqlServer",
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=NotificationsDB;Trusted_Connection=true"
  }
}
```

#### SQLite (Padrão)
```json
{
  "DatabaseProvider": "SQLite",
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=notifications.db"
  }
}
```

### SignalR

O SignalR está configurado para aceitar conexões de qualquer origem em desenvolvimento. Para produção, configure CORS adequadamente.

## 🚀 Deployment

### API
```bash
cd src/NotificationAPI
dotnet publish -c Release -o ./publish
```

### Cliente Windows Forms
```bash
cd src/NotificationClient
dotnet publish -c Release -o ./publish --self-contained true -r win-x64
```

## 🧪 Testes

Execute o exemplo de uso da API:
```bash
cd examples
dotnet run ApiUsageExample.cs
```

## 📝 Estrutura do Banco de Dados

### Tabela: Notifications

| Campo | Tipo | Descrição |
|-------|------|-----------|
| Id | int | Chave primária |
| Title | nvarchar(200) | Título da notificação |
| Message | nvarchar(1000) | Mensagem detalhada |
| UserId | nvarchar(100) | ID do usuário destinatário |
| Type | int | Tipo da notificação (0-4) |
| Priority | int | Prioridade (0-3) |
| IsRead | bit | Status de leitura |
| CreatedAt | datetime2 | Data de criação |
| ReadAt | datetime2 | Data de leitura |
| ExpiresAt | datetime2 | Data de expiração |
| ActionUrl | nvarchar(500) | URL da ação |
| ActionText | nvarchar(100) | Texto da ação |
| Metadata | nvarchar(max) | Dados extras em JSON |

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Para dúvidas ou problemas:
- Abra uma issue no GitHub
- Consulte a documentação da API em `/swagger`
- Verifique os logs da aplicação
