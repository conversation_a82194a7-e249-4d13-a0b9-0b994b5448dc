using Microsoft.AspNetCore.SignalR.Client;
using NotificationShared.DTOs;

namespace NotificationClient.Services
{
    public class SignalRService
    {
        private HubConnection? _connection;
        private readonly string _hubUrl;

        public event Action<NotificationResponseDto>? NotificationReceived;
        public event Action<int>? UnreadCountUpdated;
        public event Action<int>? NotificationRead;

        public SignalRService(string hubUrl = "https://localhost:7001/notificationHub")
        {
            _hubUrl = hubUrl;
        }

        public async Task StartConnectionAsync(string userId)
        {
            _connection = new HubConnectionBuilder()
                .WithUrl(_hubUrl)
                .Build();

            // Configurar eventos
            _connection.On<NotificationResponseDto>("ReceiveNotification", (notification) =>
            {
                NotificationReceived?.Invoke(notification);
            });

            _connection.On<int>("UnreadCountUpdated", (count) =>
            {
                UnreadCountUpdated?.Invoke(count);
            });

            _connection.On<int>("NotificationRead", (notificationId) =>
            {
                NotificationRead?.Invoke(notificationId);
            });

            // Iniciar conexão
            await _connection.StartAsync();

            // Entrar no grupo do usuário
            await _connection.InvokeAsync("JoinUserGroup", userId);
        }

        public async Task StopConnectionAsync()
        {
            if (_connection != null)
            {
                await _connection.StopAsync();
                await _connection.DisposeAsync();
                _connection = null;
            }
        }

        public bool IsConnected => _connection?.State == HubConnectionState.Connected;
    }
}
