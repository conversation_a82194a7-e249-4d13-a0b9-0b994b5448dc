# Configuração de Banco de Dados - Sistema Kodiak

## Visão Geral

O sistema suporta três tipos de banco de dados:
- **PostgreSQL** (Recomendado para produção)
- **SQL Server** (Para ambientes Microsoft)
- **SQLite** (Para desenvolvimento e testes)

## Configuração por Ambiente

### 1. PostgreSQL (Recomendado)

#### Desenvolvimento
```json
{
  "DatabaseProvider": "PostgreSQL",
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432"
  }
}
```

#### Produção
```json
{
  "DatabaseProvider": "PostgreSQL",
  "ConnectionStrings": {
    "DefaultConnection": "Host=prod-server;Database=NotificationsDB;Username=app_user;Password=secure_password;Port=5432;SSL Mode=Require"
  }
}
```

#### Docker
```json
{
  "DatabaseProvider": "PostgreSQL",
  "ConnectionStrings": {
    "DefaultConnection": "Host=********-container;Database=NotificationsDB;Username=********;Password=********;Port=5432"
  }
}
```

### 2. SQL Server

#### Desenvolvimento (LocalDB)
```json
{
  "DatabaseProvider": "SqlServer",
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=NotificationsDB;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

#### Produção
```json
{
  "DatabaseProvider": "SqlServer",
  "ConnectionStrings": {
    "DefaultConnection": "Server=prod-server;Database=NotificationsDB;User Id=app_user;Password=secure_password;TrustServerCertificate=true"
  }
}
```

#### Azure SQL
```json
{
  "DatabaseProvider": "SqlServer",
  "ConnectionStrings": {
    "DefaultConnection": "Server=tcp:server.database.windows.net,1433;Database=NotificationsDB;User ID=admin@server;Password=password;Encrypt=true;TrustServerCertificate=false;Connection Timeout=30;"
  }
}
```

### 3. SQLite

#### Desenvolvimento
```json
{
  "DatabaseProvider": "SQLite",
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=notifications.db"
  }
}
```

#### Arquivo específico
```json
{
  "DatabaseProvider": "SQLite",
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=C:\\Data\\notifications.db"
  }
}
```

## Configuração por Variáveis de Ambiente

### PostgreSQL
```bash
# Windows
set DatabaseProvider=PostgreSQL
set ConnectionStrings__DefaultConnection=Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432

# Linux/macOS
export DatabaseProvider=PostgreSQL
export ConnectionStrings__DefaultConnection="Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432"
```

### SQL Server
```bash
# Windows
set DatabaseProvider=SqlServer
set ConnectionStrings__DefaultConnection=Server=localhost;Database=NotificationsDB;Trusted_Connection=true

# Linux/macOS
export DatabaseProvider=SqlServer
export ConnectionStrings__DefaultConnection="Server=localhost;Database=NotificationsDB;Trusted_Connection=true"
```

## Scripts de Configuração

### PostgreSQL
```bash
# Setup completo
scripts/setup-********ql.bat

# Apenas criar banco
psql -U ******** -c "CREATE DATABASE \"NotificationsDB\""

# Executar scripts
psql -U ******** -d NotificationsDB -f database/********ql/create-tables.sql
```

### SQL Server
```bash
# Criar banco
sqlcmd -S localhost -Q "CREATE DATABASE NotificationsDB"

# Executar scripts (se houver)
sqlcmd -S localhost -d NotificationsDB -i database/sqlserver/create-tables.sql
```

### SQLite
```bash
# Não requer configuração - banco é criado automaticamente
```

## Migrações Entity Framework

### Criar Migração
```bash
cd src/NotificationAPI

# PostgreSQL
dotnet ef migrations add InitialCreate --context NotificationContext

# SQL Server
dotnet ef migrations add InitialCreate --context NotificationContext

# SQLite
dotnet ef migrations add InitialCreate --context NotificationContext
```

### Aplicar Migração
```bash
# Todas as bases
dotnet ef database update --context NotificationContext
```

### Remover Migração
```bash
dotnet ef migrations remove --context NotificationContext
```

## Performance e Otimização

### PostgreSQL
```sql
-- Configurações recomendadas
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET random_page_cost = 1.1;
SELECT pg_reload_conf();

-- Índices adicionais
CREATE INDEX CONCURRENTLY idx_notifications_user_priority 
ON "Notifications" ("UserId", "Priority") WHERE "IsRead" = false;
```

### SQL Server
```sql
-- Configurações recomendadas
ALTER DATABASE NotificationsDB SET AUTO_UPDATE_STATISTICS_ASYNC ON;
ALTER DATABASE NotificationsDB SET PAGE_VERIFY CHECKSUM;

-- Índices adicionais
CREATE NONCLUSTERED INDEX IX_Notifications_User_Priority
ON Notifications (UserId, Priority) WHERE IsRead = 0;
```

### SQLite
```sql
-- Configurações recomendadas
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;

-- Índices adicionais
CREATE INDEX idx_notifications_user_priority 
ON Notifications (UserId, Priority) WHERE IsRead = 0;
```

## Monitoramento

### PostgreSQL
```sql
-- Conexões ativas
SELECT * FROM pg_stat_activity WHERE datname = 'NotificationsDB';

-- Tamanho do banco
SELECT pg_size_pretty(pg_database_size('NotificationsDB'));

-- Estatísticas de consultas
SELECT * FROM pg_stat_statements WHERE query LIKE '%Notifications%';
```

### SQL Server
```sql
-- Conexões ativas
SELECT * FROM sys.dm_exec_sessions WHERE database_id = DB_ID('NotificationsDB');

-- Tamanho do banco
SELECT 
    DB_NAME(database_id) AS DatabaseName,
    (size * 8.0 / 1024) AS SizeMB
FROM sys.master_files 
WHERE database_id = DB_ID('NotificationsDB');

-- Estatísticas de consultas
SELECT * FROM sys.dm_exec_query_stats;
```

### SQLite
```sql
-- Informações do banco
PRAGMA database_list;
PRAGMA table_info(Notifications);

-- Estatísticas
ANALYZE;
SELECT * FROM sqlite_stat1;
```

## Backup e Restore

### PostgreSQL
```bash
# Backup
pg_dump -U ******** -d NotificationsDB > backup.sql
pg_dump -U ******** -d NotificationsDB -Fc > backup.dump

# Restore
psql -U ******** -d NotificationsDB < backup.sql
pg_restore -U ******** -d NotificationsDB backup.dump
```

### SQL Server
```sql
-- Backup
BACKUP DATABASE NotificationsDB 
TO DISK = 'C:\Backup\NotificationsDB.bak';

-- Restore
RESTORE DATABASE NotificationsDB 
FROM DISK = 'C:\Backup\NotificationsDB.bak';
```

### SQLite
```bash
# Backup (simples cópia)
copy notifications.db notifications_backup.db

# Backup via SQL
sqlite3 notifications.db ".backup backup.db"

# Restore
copy notifications_backup.db notifications.db
```

## Solução de Problemas

### Erro de Conexão PostgreSQL
```bash
# Verificar se está rodando
sudo systemctl status ********ql

# Verificar porta
netstat -an | grep 5432

# Testar conexão
psql -U ******** -h localhost -p 5432
```

### Erro de Conexão SQL Server
```bash
# Verificar serviços
net start | findstr SQL

# Testar conexão
sqlcmd -S localhost -E
```

### Erro SQLite
```bash
# Verificar permissões
ls -la notifications.db

# Verificar integridade
sqlite3 notifications.db "PRAGMA integrity_check;"
```

## Configuração Docker

### docker-compose.yml
```yaml
version: '3.8'
services:
  # PostgreSQL
  ********:
    image: ********:15
    environment:
      POSTGRES_DB: NotificationsDB
      POSTGRES_USER: ********
      POSTGRES_PASSWORD: ********
    ports:
      - "5432:5432"
    volumes:
      - ********_data:/var/lib/********ql/data

  # SQL Server
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    environment:
      SA_PASSWORD: "YourStrong@Passw0rd"
      ACCEPT_EULA: "Y"
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql

volumes:
  ********_data:
  sqlserver_data:
```

## Segurança

### PostgreSQL
```sql
-- Criar usuário específico
CREATE USER notificationapp WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE "NotificationsDB" TO notificationapp;
GRANT USAGE ON SCHEMA public TO notificationapp;
GRANT SELECT, INSERT, UPDATE, DELETE ON "Notifications" TO notificationapp;
```

### SQL Server
```sql
-- Criar login e usuário
CREATE LOGIN notificationapp WITH PASSWORD = 'SecurePassword123!';
USE NotificationsDB;
CREATE USER notificationapp FOR LOGIN notificationapp;
ALTER ROLE db_datareader ADD MEMBER notificationapp;
ALTER ROLE db_datawriter ADD MEMBER notificationapp;
```

### Criptografia de Connection String
```csharp
// Usar Azure Key Vault ou similar
var connectionString = await keyVaultClient.GetSecretAsync("ConnectionString");
```
