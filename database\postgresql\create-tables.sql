-- <PERSON>ript para criar tabelas no PostgreSQL
-- Execute após criar o banco de dados

-- Conectar ao banco
\c "NotificationsDB";

-- Criar tabela de notificações
CREATE TABLE IF NOT EXISTS "Notifications" (
    "Id" SERIAL PRIMARY KEY,
    "Title" VARCHAR(200) NOT NULL,
    "Message" VARCHAR(1000) NOT NULL,
    "UserId" VARCHAR(100) NOT NULL,
    "Type" INTEGER NOT NULL DEFAULT 0,
    "Priority" INTEGER NOT NULL DEFAULT 1,
    "IsRead" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "ReadAt" TIMESTAMP WITH TIME ZONE NULL,
    "ExpiresAt" TIMESTAMP WITH TIME ZONE NULL,
    "ActionUrl" VARCHAR(500) NULL,
    "ActionText" VARCHAR(100) NULL,
    "Metadata" TEXT NULL
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS "IX_Notifications_UserId" ON "Notifications" ("UserId");
CREATE INDEX IF NOT EXISTS "IX_Notifications_IsRead" ON "Notifications" ("IsRead");
CREATE INDEX IF NOT EXISTS "IX_Notifications_CreatedAt" ON "Notifications" ("CreatedAt");
CREATE INDEX IF NOT EXISTS "IX_Notifications_UserId_IsRead" ON "Notifications" ("UserId", "IsRead");
CREATE INDEX IF NOT EXISTS "IX_Notifications_Type" ON "Notifications" ("Type");
CREATE INDEX IF NOT EXISTS "IX_Notifications_Priority" ON "Notifications" ("Priority");
CREATE INDEX IF NOT EXISTS "IX_Notifications_ExpiresAt" ON "Notifications" ("ExpiresAt");

-- Comentários nas colunas
COMMENT ON TABLE "Notifications" IS 'Tabela de notificações do sistema';
COMMENT ON COLUMN "Notifications"."Id" IS 'Identificador único da notificação';
COMMENT ON COLUMN "Notifications"."Title" IS 'Título da notificação';
COMMENT ON COLUMN "Notifications"."Message" IS 'Mensagem detalhada da notificação';
COMMENT ON COLUMN "Notifications"."UserId" IS 'ID do usuário destinatário';
COMMENT ON COLUMN "Notifications"."Type" IS 'Tipo da notificação (0=Info, 1=Success, 2=Warning, 3=Error, 4=System)';
COMMENT ON COLUMN "Notifications"."Priority" IS 'Prioridade (0=Low, 1=Normal, 2=High, 3=Critical)';
COMMENT ON COLUMN "Notifications"."IsRead" IS 'Indica se a notificação foi lida';
COMMENT ON COLUMN "Notifications"."CreatedAt" IS 'Data e hora de criação';
COMMENT ON COLUMN "Notifications"."ReadAt" IS 'Data e hora de leitura';
COMMENT ON COLUMN "Notifications"."ExpiresAt" IS 'Data e hora de expiração';
COMMENT ON COLUMN "Notifications"."ActionUrl" IS 'URL para ação da notificação';
COMMENT ON COLUMN "Notifications"."ActionText" IS 'Texto do botão de ação';
COMMENT ON COLUMN "Notifications"."Metadata" IS 'Dados extras em formato JSON';

-- Inserir dados de exemplo
INSERT INTO "Notifications" ("Title", "Message", "UserId", "Type", "Priority", "CreatedAt") VALUES
('Bem-vindo ao Sistema!', 'Esta é sua primeira notificação no sistema Kodiak.', 'user1', 0, 1, NOW()),
('Operação Realizada', 'Sua operação foi realizada com sucesso.', 'user1', 1, 1, NOW()),
('Atenção Necessária', 'Verifique os dados antes de continuar.', 'user1', 2, 2, NOW()),
('Erro no Sistema', 'Ocorreu um erro. Contate o administrador.', 'user1', 3, 3, NOW());

-- Verificar dados inseridos
SELECT * FROM "Notifications" ORDER BY "CreatedAt" DESC;
