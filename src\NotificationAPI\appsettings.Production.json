{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}}, "DatabaseProvider": "PostgreSQL", "ConnectionStrings": {"DefaultConnection": "Host=${DB_HOST};Database=${DB_NAME};Username=${DB_USER};Password=${DB_PASSWORD};Port=${DB_PORT};SSL Mode=Require;Trust Server Certificate=true", "PostgreSQLConnection": "Host=${DB_HOST};Database=${DB_NAME};Username=${DB_USER};Password=${DB_PASSWORD};Port=${DB_PORT};SSL Mode=Require;Trust Server Certificate=true"}, "AllowedHosts": "*"}