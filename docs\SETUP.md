# Guia de Configuração - Sistema de Notificações Kodiak

## Pré-requisitos

### Software Necessário
- **.NET 8.0 SDK** - [Download](https://dotnet.microsoft.com/download/dotnet/8.0)
- **Visual Studio 2022** ou **VS Code** - [Download VS](https://visualstudio.microsoft.com/)
- **Git** - [Download](https://git-scm.com/)

### Opcional
- **SQL Server** ou **SQL Server Express** - Para produção
- **Postman** - Para testar a API

## Configuração do Ambiente

### 1. Clone o Repositório
```bash
git clone <repository-url>
cd NotificacoesKodiak
```

### 2. Restaurar Dependências
```bash
dotnet restore
```

### 3. Compilar Projetos
```bash
# Compilar todos os projetos
scripts/build-all.bat

# Ou compilar individualmente
cd src/NotificationShared && dotnet build
cd ../NotificationAPI && dotnet build
cd ../NotificationClient && dotnet build
```

## Configuração do Banco de Dados

### Opção 1: SQLite (Padrão - Recomendado para Desenvolvimento)
Não requer configuração adicional. O banco será criado automaticamente.

### Opção 2: SQL Server
1. **Instale o SQL Server** ou use SQL Server Express
2. **Edite `appsettings.json`** na API:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=NotificationsDB;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

3. **Execute as migrações** (se necessário):
```bash
cd src/NotificationAPI
dotnet ef database update
```

## Executando o Sistema

### Método 1: Scripts Automatizados
```bash
# Terminal 1 - API
scripts/start-api.bat

# Terminal 2 - Cliente
scripts/start-client.bat
```

### Método 2: Manual
```bash
# Terminal 1 - API
cd src/NotificationAPI
dotnet run

# Terminal 2 - Cliente
cd src/NotificationClient
dotnet run
```

### Método 3: Visual Studio
1. Abra `NotificacoesKodiak.sln`
2. Configure múltiplos projetos de inicialização:
   - NotificationAPI
   - NotificationClient
3. Pressione F5

## Verificação da Instalação

### 1. API Funcionando
- Acesse: `https://localhost:7001/swagger`
- Deve exibir a documentação da API

### 2. SignalR Funcionando
- No cliente, conecte com um User ID
- Status deve mostrar "Conectado"

### 3. Banco de Dados
- Verifique se o arquivo `notifications.db` foi criado na pasta da API

## Configurações Avançadas

### CORS (Para Produção)
Edite `Program.cs` na API:
```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("Production", policy =>
    {
        policy.WithOrigins("https://yourdomain.com")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});
```

### HTTPS Certificates
Para desenvolvimento, confie no certificado:
```bash
dotnet dev-certs https --trust
```

### Logging
Configure níveis de log em `appsettings.json`:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  }
}
```

## Solução de Problemas

### Erro: "Unable to bind to https://localhost:7001"
- Verifique se a porta está livre
- Execute: `netstat -ano | findstr :7001`
- Mate o processo se necessário

### Erro: "Database connection failed"
- Verifique a string de conexão
- Para SQLite, verifique permissões de escrita na pasta

### Erro: "SignalR connection failed"
- Verifique se a API está rodando
- Confirme a URL do hub no cliente

### Cliente não recebe notificações em tempo real
- Verifique conexão SignalR
- Confirme que o User ID está correto
- Verifique logs da API

## Desenvolvimento

### Estrutura de Pastas Recomendada
```
NotificacoesKodiak/
├── src/
│   ├── NotificationAPI/
│   │   ├── Controllers/
│   │   ├── Data/
│   │   ├── Hubs/
│   │   └── Services/
│   ├── NotificationClient/
│   │   ├── Forms/
│   │   └── Services/
│   └── NotificationShared/
│       ├── DTOs/
│       └── Models/
```

### Adicionando Novos Endpoints
1. Adicione método no `INotificationService`
2. Implemente no `NotificationService`
3. Adicione endpoint no `NotificationsController`
4. Atualize cliente se necessário

### Adicionando Novos Campos
1. Atualize modelo em `NotificationShared`
2. Adicione migração EF (se usando SQL Server)
3. Atualize DTOs
4. Atualize interface do cliente

## Performance

### Otimizações Recomendadas
- Use paginação para listas grandes
- Implemente cache para consultas frequentes
- Configure índices no banco de dados
- Use compressão SignalR para produção

### Monitoramento
- Configure Application Insights (Azure)
- Use Serilog para logs estruturados
- Monitore conexões SignalR ativas

## Segurança

### Recomendações
- Implemente autenticação JWT
- Valide todos os inputs
- Use HTTPS em produção
- Configure rate limiting
- Sanitize dados de entrada

### Exemplo de Autenticação
```csharp
// No Program.cs
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options => {
        // Configuração JWT
    });
```
