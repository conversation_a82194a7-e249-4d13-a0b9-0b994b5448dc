using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using NotificationShared.DTOs;
using NotificationShared.Models;

namespace Examples
{
    /// <summary>
    /// Exemplo de como usar a API de Notificações programaticamente
    /// </summary>
    public class ApiUsageExample
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public ApiUsageExample(string baseUrl = "https://localhost:7001")
        {
            _httpClient = new HttpClient();
            _baseUrl = baseUrl;
        }

        /// <summary>
        /// Exemplo completo de uso da API
        /// </summary>
        public async Task RunExampleAsync()
        {
            try
            {
                Console.WriteLine("=== Exemplo de Uso da API de Notificações ===\n");

                // 1. Criar uma notificação
                Console.WriteLine("1. Criando notificação...");
                var notification = await CreateNotificationAsync();
                Console.WriteLine($"Notificação criada com ID: {notification?.Id}\n");

                // 2. Listar notificações do usuário
                Console.WriteLine("2. Listando notificações do usuário...");
                await ListUserNotificationsAsync("user1");

                // 3. Obter contagem de não lidas
                Console.WriteLine("3. Obtendo contagem de não lidas...");
                var unreadCount = await GetUnreadCountAsync("user1");
                Console.WriteLine($"Notificações não lidas: {unreadCount}\n");

                // 4. Marcar como lida
                if (notification != null)
                {
                    Console.WriteLine("4. Marcando notificação como lida...");
                    await MarkAsReadAsync(notification.Id, "user1");
                    Console.WriteLine("Notificação marcada como lida.\n");
                }

                // 5. Criar notificação de diferentes tipos
                Console.WriteLine("5. Criando notificações de diferentes tipos...");
                await CreateDifferentTypesAsync();

                Console.WriteLine("Exemplo concluído!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro: {ex.Message}");
            }
        }

        private async Task<NotificationResponseDto?> CreateNotificationAsync()
        {
            var notification = new CreateNotificationDto
            {
                Title = "Bem-vindo ao Sistema!",
                Message = "Esta é sua primeira notificação no sistema Kodiak.",
                UserId = "user1",
                Type = NotificationType.Info,
                Priority = NotificationPriority.Normal,
                ActionText = "Ver Detalhes",
                ActionUrl = "https://example.com/details"
            };

            var json = JsonConvert.SerializeObject(notification);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/api/notifications", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<NotificationResponseDto>(responseJson);
            }

            return null;
        }

        private async Task ListUserNotificationsAsync(string userId)
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/notifications?userId={userId}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var notifications = JsonConvert.DeserializeObject<List<NotificationResponseDto>>(json);

                Console.WriteLine($"Encontradas {notifications?.Count ?? 0} notificações:");
                
                if (notifications != null)
                {
                    foreach (var notif in notifications)
                    {
                        Console.WriteLine($"  - {notif.Title} ({notif.Type}) - {(notif.IsRead ? "Lida" : "Não lida")}");
                    }
                }
                Console.WriteLine();
            }
        }

        private async Task<int> GetUnreadCountAsync(string userId)
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/notifications/unread-count?userId={userId}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<int>(json);
            }

            return 0;
        }

        private async Task MarkAsReadAsync(int notificationId, string userId)
        {
            var response = await _httpClient.PostAsync($"{_baseUrl}/api/notifications/{notificationId}/mark-read?userId={userId}", null);
            
            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine("Erro ao marcar como lida.");
            }
        }

        private async Task CreateDifferentTypesAsync()
        {
            var notifications = new[]
            {
                new CreateNotificationDto
                {
                    Title = "Sucesso!",
                    Message = "Operação realizada com sucesso.",
                    UserId = "user1",
                    Type = NotificationType.Success,
                    Priority = NotificationPriority.Normal
                },
                new CreateNotificationDto
                {
                    Title = "Atenção!",
                    Message = "Verifique os dados antes de continuar.",
                    UserId = "user1",
                    Type = NotificationType.Warning,
                    Priority = NotificationPriority.High
                },
                new CreateNotificationDto
                {
                    Title = "Erro Crítico!",
                    Message = "Falha no sistema. Contate o administrador.",
                    UserId = "user1",
                    Type = NotificationType.Error,
                    Priority = NotificationPriority.Critical
                }
            };

            foreach (var notification in notifications)
            {
                var json = JsonConvert.SerializeObject(notification);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                await _httpClient.PostAsync($"{_baseUrl}/api/notifications", content);
                Console.WriteLine($"Criada: {notification.Title}");
            }
            Console.WriteLine();
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    // Programa de exemplo
    class Program
    {
        static async Task Main(string[] args)
        {
            var example = new ApiUsageExample();
            
            try
            {
                await example.RunExampleAsync();
            }
            finally
            {
                example.Dispose();
            }

            Console.WriteLine("Pressione qualquer tecla para sair...");
            Console.ReadKey();
        }
    }
}
