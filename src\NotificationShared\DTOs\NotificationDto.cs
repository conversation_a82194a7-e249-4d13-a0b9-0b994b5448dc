using NotificationShared.Models;

namespace NotificationShared.DTOs
{
    public class CreateNotificationDto
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public NotificationType Type { get; set; } = NotificationType.Info;
        public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
        public DateTime? ExpiresAt { get; set; }
        public string? ActionUrl { get; set; }
        public string? ActionText { get; set; }
        public string? Metadata { get; set; }
    }
    
    public class UpdateNotificationDto
    {
        public bool? IsRead { get; set; }
        public string? Title { get; set; }
        public string? Message { get; set; }
    }
    
    public class NotificationResponseDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public NotificationPriority Priority { get; set; }
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ReadAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? ActionUrl { get; set; }
        public string? ActionText { get; set; }
        public string? Metadata { get; set; }
        
        public static NotificationResponseDto FromNotification(Notification notification)
        {
            return new NotificationResponseDto
            {
                Id = notification.Id,
                Title = notification.Title,
                Message = notification.Message,
                UserId = notification.UserId,
                Type = notification.Type,
                Priority = notification.Priority,
                IsRead = notification.IsRead,
                CreatedAt = notification.CreatedAt,
                ReadAt = notification.ReadAt,
                ExpiresAt = notification.ExpiresAt,
                ActionUrl = notification.ActionUrl,
                ActionText = notification.ActionText,
                Metadata = notification.Metadata
            };
        }
    }
    
    public class NotificationQueryDto
    {
        public string? UserId { get; set; }
        public bool? IsRead { get; set; }
        public NotificationType? Type { get; set; }
        public NotificationPriority? Priority { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}
