using System.ComponentModel.DataAnnotations;

namespace NotificationShared.Models
{
    public class Notification
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(1000)]
        public string Message { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string UserId { get; set; } = string.Empty;
        
        public NotificationType Type { get; set; } = NotificationType.Info;
        
        public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
        
        public bool IsRead { get; set; } = false;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? ReadAt { get; set; }
        
        public DateTime? ExpiresAt { get; set; }
        
        [MaxLength(500)]
        public string? ActionUrl { get; set; }
        
        [MaxLength(100)]
        public string? ActionText { get; set; }
        
        public string? Metadata { get; set; } // JSON para dados extras
    }
    
    public enum NotificationType
    {
        Info = 0,
        Success = 1,
        Warning = 2,
        Error = 3,
        System = 4
    }
    
    public enum NotificationPriority
    {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    }
}
