using Newtonsoft.Json;
using NotificationShared.DTOs;
using System.Text;

namespace NotificationClient.Services
{
    public class NotificationApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public NotificationApiClient(string baseUrl = "https://localhost:7001")
        {
            _httpClient = new HttpClient();
            _baseUrl = baseUrl;
        }

        public async Task<NotificationResponseDto?> CreateNotificationAsync(CreateNotificationDto notification)
        {
            var json = JsonConvert.SerializeObject(notification);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"{_baseUrl}/api/notifications", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<NotificationResponseDto>(responseJson);
            }

            return null;
        }

        public async Task<List<NotificationResponseDto>> GetNotificationsAsync(string userId, bool? isRead = null)
        {
            var url = $"{_baseUrl}/api/notifications?userId={userId}";
            if (isRead.HasValue)
                url += $"&isRead={isRead.Value}";

            var response = await _httpClient.GetAsync(url);
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<NotificationResponseDto>>(json) ?? new List<NotificationResponseDto>();
            }

            return new List<NotificationResponseDto>();
        }

        public async Task<bool> MarkAsReadAsync(int notificationId, string userId)
        {
            var response = await _httpClient.PostAsync($"{_baseUrl}/api/notifications/{notificationId}/mark-read?userId={userId}", null);
            return response.IsSuccessStatusCode;
        }

        public async Task<bool> MarkAsUnreadAsync(int notificationId, string userId)
        {
            var response = await _httpClient.PostAsync($"{_baseUrl}/api/notifications/{notificationId}/mark-unread?userId={userId}", null);
            return response.IsSuccessStatusCode;
        }

        public async Task<int> GetUnreadCountAsync(string userId)
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/notifications/unread-count?userId={userId}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<int>(json);
            }

            return 0;
        }

        public async Task<bool> MarkAllAsReadAsync(string userId)
        {
            var response = await _httpClient.PostAsync($"{_baseUrl}/api/notifications/mark-all-read?userId={userId}", null);
            return response.IsSuccessStatusCode;
        }

        public async Task<List<NotificationResponseDto>> GetRecentNotificationsAsync(string userId, int count = 10)
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/notifications/recent?userId={userId}&count={count}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<NotificationResponseDto>>(json) ?? new List<NotificationResponseDto>();
            }

            return new List<NotificationResponseDto>();
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
