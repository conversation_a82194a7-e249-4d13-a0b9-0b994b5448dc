using Microsoft.EntityFrameworkCore;
using NotificationAPI.Data;
using NotificationAPI.Hubs;
using NotificationShared.DTOs;
using NotificationShared.Models;

namespace NotificationAPI.Services
{
    public class NotificationService : INotificationService
    {
        private readonly NotificationContext _context;
        private readonly INotificationHubService _hubService;

        public NotificationService(NotificationContext context, INotificationHubService hubService)
        {
            _context = context;
            _hubService = hubService;
        }

        public async Task<NotificationResponseDto> CreateNotificationAsync(CreateNotificationDto dto)
        {
            var notification = new Notification
            {
                Title = dto.Title,
                Message = dto.Message,
                UserId = dto.UserId,
                Type = dto.Type,
                Priority = dto.Priority,
                ExpiresAt = dto.ExpiresAt,
                ActionUrl = dto.ActionUrl,
                ActionText = dto.ActionText,
                Metadata = dto.Metadata,
                CreatedAt = DateTime.UtcNow
            };

            _context.Notifications.Add(notification);
            await _context.SaveChangesAsync();

            var response = NotificationResponseDto.FromNotification(notification);

            // Enviar notificação em tempo real
            await _hubService.SendNotificationToUserAsync(notification.UserId, response);

            // Atualizar contagem de não lidas
            var unreadCount = await GetUnreadCountAsync(notification.UserId);
            await _hubService.SendUnreadCountUpdateAsync(notification.UserId, unreadCount);

            return response;
        }

        public async Task<NotificationResponseDto?> GetNotificationByIdAsync(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            return notification != null ? NotificationResponseDto.FromNotification(notification) : null;
        }

        public async Task<IEnumerable<NotificationResponseDto>> GetNotificationsAsync(NotificationQueryDto query)
        {
            var queryable = _context.Notifications.AsQueryable();

            if (!string.IsNullOrEmpty(query.UserId))
                queryable = queryable.Where(n => n.UserId == query.UserId);

            if (query.IsRead.HasValue)
                queryable = queryable.Where(n => n.IsRead == query.IsRead.Value);

            if (query.Type.HasValue)
                queryable = queryable.Where(n => n.Type == query.Type.Value);

            if (query.Priority.HasValue)
                queryable = queryable.Where(n => n.Priority == query.Priority.Value);

            if (query.FromDate.HasValue)
                queryable = queryable.Where(n => n.CreatedAt >= query.FromDate.Value);

            if (query.ToDate.HasValue)
                queryable = queryable.Where(n => n.CreatedAt <= query.ToDate.Value);

            // Filtrar notificações expiradas
            queryable = queryable.Where(n => n.ExpiresAt == null || n.ExpiresAt > DateTime.UtcNow);

            var notifications = await queryable
                .OrderByDescending(n => n.CreatedAt)
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            return notifications.Select(NotificationResponseDto.FromNotification);
        }

        public async Task<NotificationResponseDto?> UpdateNotificationAsync(int id, UpdateNotificationDto dto)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null) return null;

            if (dto.IsRead.HasValue)
            {
                notification.IsRead = dto.IsRead.Value;
                notification.ReadAt = dto.IsRead.Value ? DateTime.UtcNow : null;
            }

            if (!string.IsNullOrEmpty(dto.Title))
                notification.Title = dto.Title;

            if (!string.IsNullOrEmpty(dto.Message))
                notification.Message = dto.Message;

            await _context.SaveChangesAsync();
            return NotificationResponseDto.FromNotification(notification);
        }

        public async Task<bool> DeleteNotificationAsync(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null) return false;

            _context.Notifications.Remove(notification);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkAsReadAsync(int id, string userId)
        {
            var notification = await _context.Notifications
                .FirstOrDefaultAsync(n => n.Id == id && n.UserId == userId);

            if (notification == null) return false;

            notification.IsRead = true;
            notification.ReadAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            // Notificar via SignalR
            await _hubService.SendNotificationReadAsync(userId, id);

            // Atualizar contagem de não lidas
            var unreadCount = await GetUnreadCountAsync(userId);
            await _hubService.SendUnreadCountUpdateAsync(userId, unreadCount);

            return true;
        }

        public async Task<bool> MarkAsUnreadAsync(int id, string userId)
        {
            var notification = await _context.Notifications
                .FirstOrDefaultAsync(n => n.Id == id && n.UserId == userId);

            if (notification == null) return false;

            notification.IsRead = false;
            notification.ReadAt = null;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<int> GetUnreadCountAsync(string userId)
        {
            return await _context.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .Where(n => n.ExpiresAt == null || n.ExpiresAt > DateTime.UtcNow)
                .CountAsync();
        }

        public async Task<bool> MarkAllAsReadAsync(string userId)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .ToListAsync();

            foreach (var notification in notifications)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<NotificationResponseDto>> GetRecentNotificationsAsync(string userId, int count = 10)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UserId == userId)
                .Where(n => n.ExpiresAt == null || n.ExpiresAt > DateTime.UtcNow)
                .OrderByDescending(n => n.CreatedAt)
                .Take(count)
                .ToListAsync();

            return notifications.Select(NotificationResponseDto.FromNotification);
        }
    }
}
