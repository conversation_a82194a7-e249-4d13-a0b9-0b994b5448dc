using NotificationClient.Services;
using NotificationShared.DTOs;
using NotificationShared.Models;

namespace NotificationClient.Forms
{
    public partial class CreateNotificationForm : Form
    {
        private readonly NotificationApiClient _apiClient;
        private readonly string _defaultUserId;

        private TextBox _titleTextBox;
        private TextBox _messageTextBox;
        private TextBox _userIdTextBox;
        private ComboBox _typeComboBox;
        private ComboBox _priorityComboBox;
        private DateTimePicker _expiresAtPicker;
        private CheckBox _hasExpirationCheckBox;
        private TextBox _actionUrlTextBox;
        private TextBox _actionTextTextBox;
        private Button _createButton;
        private Button _cancelButton;

        public CreateNotificationForm(NotificationApiClient apiClient, string defaultUserId)
        {
            _apiClient = apiClient;
            _defaultUserId = defaultUserId;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Criar Nova Notificação";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var y = 20;
            var labelWidth = 100;
            var controlWidth = 350;
            var spacing = 35;

            // Título
            var titleLabel = new Label
            {
                Text = "Título:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23)
            };

            _titleTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23)
            };

            y += spacing;

            // Mensagem
            var messageLabel = new Label
            {
                Text = "Mensagem:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23)
            };

            _messageTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            y += 90;

            // User ID
            var userIdLabel = new Label
            {
                Text = "User ID:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23)
            };

            _userIdTextBox = new TextBox
            {
                Text = _defaultUserId,
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23)
            };

            y += spacing;

            // Tipo
            var typeLabel = new Label
            {
                Text = "Tipo:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23)
            };

            _typeComboBox = new ComboBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            _typeComboBox.Items.AddRange(Enum.GetNames(typeof(NotificationType)));
            _typeComboBox.SelectedIndex = 0;

            y += spacing;

            // Prioridade
            var priorityLabel = new Label
            {
                Text = "Prioridade:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23)
            };

            _priorityComboBox = new ComboBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            _priorityComboBox.Items.AddRange(Enum.GetNames(typeof(NotificationPriority)));
            _priorityComboBox.SelectedIndex = 1; // Normal

            y += spacing;

            // Expiração
            _hasExpirationCheckBox = new CheckBox
            {
                Text = "Tem expiração",
                Location = new Point(20, y),
                Size = new Size(120, 23)
            };
            _hasExpirationCheckBox.CheckedChanged += HasExpirationCheckBox_CheckedChanged;

            _expiresAtPicker = new DateTimePicker
            {
                Location = new Point(150, y),
                Size = new Size(200, 23),
                Enabled = false,
                Value = DateTime.Now.AddDays(7)
            };

            y += spacing;

            // Action URL
            var actionUrlLabel = new Label
            {
                Text = "URL Ação:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23)
            };

            _actionUrlTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23)
            };

            y += spacing;

            // Action Text
            var actionTextLabel = new Label
            {
                Text = "Texto Ação:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23)
            };

            _actionTextTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23)
            };

            y += spacing + 20;

            // Botões
            _createButton = new Button
            {
                Text = "Criar",
                Location = new Point(300, y),
                Size = new Size(80, 30),
                DialogResult = DialogResult.OK
            };
            _createButton.Click += CreateButton_Click;

            _cancelButton = new Button
            {
                Text = "Cancelar",
                Location = new Point(390, y),
                Size = new Size(80, 30),
                DialogResult = DialogResult.Cancel
            };

            this.Controls.AddRange(new Control[] {
                titleLabel, _titleTextBox,
                messageLabel, _messageTextBox,
                userIdLabel, _userIdTextBox,
                typeLabel, _typeComboBox,
                priorityLabel, _priorityComboBox,
                _hasExpirationCheckBox, _expiresAtPicker,
                actionUrlLabel, _actionUrlTextBox,
                actionTextLabel, _actionTextTextBox,
                _createButton, _cancelButton
            });

            this.AcceptButton = _createButton;
            this.CancelButton = _cancelButton;
        }

        private void HasExpirationCheckBox_CheckedChanged(object? sender, EventArgs e)
        {
            _expiresAtPicker.Enabled = _hasExpirationCheckBox.Checked;
        }

        private async void CreateButton_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(_titleTextBox.Text))
            {
                MessageBox.Show("Título é obrigatório.");
                return;
            }

            if (string.IsNullOrWhiteSpace(_messageTextBox.Text))
            {
                MessageBox.Show("Mensagem é obrigatória.");
                return;
            }

            if (string.IsNullOrWhiteSpace(_userIdTextBox.Text))
            {
                MessageBox.Show("User ID é obrigatório.");
                return;
            }

            try
            {
                var dto = new CreateNotificationDto
                {
                    Title = _titleTextBox.Text.Trim(),
                    Message = _messageTextBox.Text.Trim(),
                    UserId = _userIdTextBox.Text.Trim(),
                    Type = (NotificationType)Enum.Parse(typeof(NotificationType), _typeComboBox.SelectedItem.ToString()),
                    Priority = (NotificationPriority)Enum.Parse(typeof(NotificationPriority), _priorityComboBox.SelectedItem.ToString()),
                    ExpiresAt = _hasExpirationCheckBox.Checked ? _expiresAtPicker.Value : null,
                    ActionUrl = string.IsNullOrWhiteSpace(_actionUrlTextBox.Text) ? null : _actionUrlTextBox.Text.Trim(),
                    ActionText = string.IsNullOrWhiteSpace(_actionTextTextBox.Text) ? null : _actionTextTextBox.Text.Trim()
                };

                _createButton.Enabled = false;
                _createButton.Text = "Criando...";

                var result = await _apiClient.CreateNotificationAsync(dto);

                if (result != null)
                {
                    MessageBox.Show("Notificação criada com sucesso!");
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Erro ao criar notificação.");
                    _createButton.Enabled = true;
                    _createButton.Text = "Criar";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao criar notificação: {ex.Message}");
                _createButton.Enabled = true;
                _createButton.Text = "Criar";
            }
        }
    }
}
