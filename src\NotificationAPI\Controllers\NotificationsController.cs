using Microsoft.AspNetCore.Mvc;
using NotificationAPI.Services;
using NotificationShared.DTOs;

namespace NotificationAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class NotificationsController : ControllerBase
    {
        private readonly INotificationService _notificationService;

        public NotificationsController(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        /// <summary>
        /// Criar uma nova notificação
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<NotificationResponseDto>> CreateNotification([FromBody] CreateNotificationDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var notification = await _notificationService.CreateNotificationAsync(dto);
            return CreatedAtAction(nameof(GetNotification), new { id = notification.Id }, notification);
        }

        /// <summary>
        /// Obter notificação por ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<NotificationResponseDto>> GetNotification(int id)
        {
            var notification = await _notificationService.GetNotificationByIdAsync(id);
            if (notification == null)
                return NotFound();

            return Ok(notification);
        }

        /// <summary>
        /// Listar notificações com filtros
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<NotificationResponseDto>>> GetNotifications([FromQuery] NotificationQueryDto query)
        {
            var notifications = await _notificationService.GetNotificationsAsync(query);
            return Ok(notifications);
        }

        /// <summary>
        /// Atualizar notificação
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<NotificationResponseDto>> UpdateNotification(int id, [FromBody] UpdateNotificationDto dto)
        {
            var notification = await _notificationService.UpdateNotificationAsync(id, dto);
            if (notification == null)
                return NotFound();

            return Ok(notification);
        }

        /// <summary>
        /// Deletar notificação
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteNotification(int id)
        {
            var success = await _notificationService.DeleteNotificationAsync(id);
            if (!success)
                return NotFound();

            return NoContent();
        }

        /// <summary>
        /// Marcar notificação como lida
        /// </summary>
        [HttpPost("{id}/mark-read")]
        public async Task<ActionResult> MarkAsRead(int id, [FromQuery] string userId)
        {
            if (string.IsNullOrEmpty(userId))
                return BadRequest("UserId é obrigatório");

            var success = await _notificationService.MarkAsReadAsync(id, userId);
            if (!success)
                return NotFound();

            return Ok();
        }

        /// <summary>
        /// Marcar notificação como não lida
        /// </summary>
        [HttpPost("{id}/mark-unread")]
        public async Task<ActionResult> MarkAsUnread(int id, [FromQuery] string userId)
        {
            if (string.IsNullOrEmpty(userId))
                return BadRequest("UserId é obrigatório");

            var success = await _notificationService.MarkAsUnreadAsync(id, userId);
            if (!success)
                return NotFound();

            return Ok();
        }

        /// <summary>
        /// Obter contagem de notificações não lidas
        /// </summary>
        [HttpGet("unread-count")]
        public async Task<ActionResult<int>> GetUnreadCount([FromQuery] string userId)
        {
            if (string.IsNullOrEmpty(userId))
                return BadRequest("UserId é obrigatório");

            var count = await _notificationService.GetUnreadCountAsync(userId);
            return Ok(count);
        }

        /// <summary>
        /// Marcar todas as notificações como lidas
        /// </summary>
        [HttpPost("mark-all-read")]
        public async Task<ActionResult> MarkAllAsRead([FromQuery] string userId)
        {
            if (string.IsNullOrEmpty(userId))
                return BadRequest("UserId é obrigatório");

            await _notificationService.MarkAllAsReadAsync(userId);
            return Ok();
        }

        /// <summary>
        /// Obter notificações recentes
        /// </summary>
        [HttpGet("recent")]
        public async Task<ActionResult<IEnumerable<NotificationResponseDto>>> GetRecentNotifications(
            [FromQuery] string userId, 
            [FromQuery] int count = 10)
        {
            if (string.IsNullOrEmpty(userId))
                return BadRequest("UserId é obrigatório");

            var notifications = await _notificationService.GetRecentNotificationsAsync(userId, count);
            return Ok(notifications);
        }
    }
}
