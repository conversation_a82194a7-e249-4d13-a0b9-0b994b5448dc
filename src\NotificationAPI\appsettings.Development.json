{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "DatabaseProvider": "PostgreSQL", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432", "PostgreSQLConnection": "Host=localhost;Database=NotificationsDB;Username=********;Password=********;Port=5432"}}